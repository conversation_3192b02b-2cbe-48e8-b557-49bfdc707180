@tailwind base;
@tailwind components;
@tailwind utilities;

/* 🎯 Naive UI Integration Styles */
/* Reset some Naive UI styles to work better with Tailwind */
.n-button {
  font-family: inherit !important;
}

.n-card {
  font-family: inherit !important;
}

.n-input {
  font-family: inherit !important;
}

.n-select {
  font-family: inherit !important;
}

/* Ensure Naive UI components respect our color scheme */
.n-config-provider {
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif !important;
}

/* 🎨 CSS Variables for Dynamic Theming */
:root {
  /* Light Mode Variables */
  --bg-primary: #F8F9FA;
  --bg-secondary: #E9ECEF;
  --text-primary: #212529;
  --text-secondary: #6C757D;
  --text-muted: #ADB5BD;
  --accent-sport: #00C897;
  --accent-danger: #EF233C;
  --accent-warning: #FFC107;
  --accent-info: #17A2B8;
  --border-primary: #CED4DA;
  --border-secondary: #DEE2E6;

  /* Animation Variables */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Dark Mode Variables */
.dark {
  --bg-primary: #121212;
  --bg-secondary: #1E1E1E;
  --text-primary: #F8F9FA;
  --text-secondary: #E9ECEF;
  --text-muted: #6C757D;
  --accent-sport: #00E5A1;
  --accent-danger: #FF4757;
  --accent-warning: #FFA726;
  --accent-info: #26C6DA;
  --border-primary: #2C2C2C;
  --border-secondary: #404040;
}

@layer base {
  /* 🌐 Global Base Styles */
  *,
  *::before,
  *::after {
    box-sizing: border-box;
    border-width: 0;
    border-style: solid;
    border-color: theme('colors.light.border.primary');
  }

  html {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-feature-settings: normal;
    font-variation-settings: normal;
    @apply scroll-smooth;
  }

  body {
    @apply bg-light-bg-primary dark:bg-dark-bg-primary
           text-light-text-primary dark:text-dark-text-primary
           font-sans antialiased;
    transition: background-color var(--transition-normal), color var(--transition-normal);
  }

  /* 📝 Typography Base */
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold tracking-tight;
  }

  h1 { @apply text-4xl lg:text-5xl; }
  h2 { @apply text-3xl lg:text-4xl; }
  h3 { @apply text-2xl lg:text-3xl; }
  h4 { @apply text-xl lg:text-2xl; }
  h5 { @apply text-lg lg:text-xl; }
  h6 { @apply text-base lg:text-lg; }

  /* 🔗 Links */
  a {
    @apply text-light-accent-sport dark:text-dark-accent-sport
           hover:underline transition-colors duration-200;
  }

  /* 📝 Form Elements */
  input, textarea, select {
    @apply bg-light-bg-secondary dark:bg-dark-bg-secondary
           border border-light-border-primary dark:border-dark-border-primary
           text-light-text-primary dark:text-dark-text-primary
           placeholder:text-light-text-muted dark:placeholder:text-dark-text-muted;
  }

  /* 🎯 Focus States */
  input:focus, textarea:focus, select:focus, button:focus {
    @apply outline-none ring-2 ring-light-accent-sport dark:ring-dark-accent-sport ring-opacity-50;
  }
}

@layer components {
  /* 🔘 Button Components with Gradients */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium
           transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply btn bg-gradient-sport dark:bg-gradient-sport-dark
           text-white hover:bg-gradient-sport-hover dark:hover:bg-gradient-sport-dark-hover
           focus:ring-light-accent-sport dark:focus:ring-dark-accent-sport
           shadow-sport hover:shadow-sport-lg transform hover:scale-105 active:scale-95;
  }

  .btn-primary-animated {
    @apply btn bg-gradient-sport-animated dark:bg-gradient-sport-animated-dark
           text-white animate-gradient-x
           focus:ring-light-accent-sport dark:focus:ring-dark-accent-sport
           shadow-sport hover:shadow-sport-lg transform hover:scale-105 active:scale-95;
    background-size: 200% 200%;
  }

  .btn-danger {
    @apply btn bg-gradient-danger dark:bg-gradient-danger-dark
           text-white hover:opacity-90 focus:ring-light-accent-danger dark:focus:ring-dark-accent-danger
           shadow-danger transform hover:scale-105 active:scale-95;
  }

  /* 🎨 Gradient Text Classes */
  .text-gradient-sport {
    @apply bg-gradient-text-sport dark:bg-gradient-text-sport-dark bg-clip-text text-transparent;
  }

  .text-gradient-sport-animated {
    @apply bg-gradient-sport-animated dark:bg-gradient-sport-animated-dark
           bg-clip-text text-transparent animate-gradient-x;
    background-size: 200% 200%;
  }

  /* 🌟 Gradient Background Classes */
  .bg-gradient-sport-soft {
    @apply bg-gradient-sport-soft dark:bg-gradient-sport-dark-soft;
  }

  .bg-gradient-page {
    @apply bg-gradient-bg-sport dark:bg-gradient-bg-sport-dark;
  }

  .btn-secondary {
    @apply btn bg-light-bg-secondary dark:bg-dark-bg-secondary
           text-light-text-primary dark:text-dark-text-primary
           border border-light-border-primary dark:border-dark-border-primary
           hover:bg-light-border-primary dark:hover:bg-dark-border-primary;
  }

  .btn-ghost {
    @apply btn bg-transparent text-light-text-primary dark:text-dark-text-primary
           hover:bg-light-bg-secondary dark:hover:bg-dark-bg-secondary;
  }

  /* 📦 Card Components */
  .card {
    @apply bg-light-bg-secondary dark:bg-dark-bg-secondary
           border border-light-border-secondary dark:border-dark-border-secondary
           rounded-xl shadow-md hover:shadow-lg transition-shadow duration-200;
  }

  .card-header {
    @apply p-6 border-b border-light-border-secondary dark:border-dark-border-secondary;
  }

  .card-body {
    @apply p-6;
  }

  .card-footer {
    @apply p-6 border-t border-light-border-secondary dark:border-dark-border-secondary;
  }

  /* 📝 Form Components */
  .form-input {
    @apply w-full px-3 py-2 rounded-lg border border-light-border-primary dark:border-dark-border-primary
           bg-light-bg-primary dark:bg-dark-bg-primary
           text-light-text-primary dark:text-dark-text-primary
           placeholder:text-light-text-muted dark:placeholder:text-dark-text-muted
           focus:ring-2 focus:ring-light-accent-sport dark:focus:ring-dark-accent-sport
           focus:border-transparent transition-all duration-200;
  }

  .form-label {
    @apply block text-sm font-medium text-light-text-primary dark:text-dark-text-primary mb-2;
  }

  /* 🏷️ Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-light-accent-sport/10 dark:bg-dark-accent-sport/10
           text-light-accent-sport dark:text-dark-accent-sport;
  }

  .badge-danger {
    @apply badge bg-light-accent-danger/10 dark:bg-dark-accent-danger/10
           text-light-accent-danger dark:text-dark-accent-danger;
  }

  .badge-warning {
    @apply badge bg-light-accent-warning/10 dark:bg-dark-accent-warning/10
           text-light-accent-warning dark:text-dark-accent-warning;
  }

  /* 🎯 Navigation Components */
  .nav-link {
    @apply px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200
           text-light-text-secondary dark:text-dark-text-secondary
           hover:text-light-accent-sport dark:hover:text-dark-accent-sport
           hover:bg-light-bg-secondary dark:hover:bg-dark-bg-secondary;
  }

  .nav-link.active {
    @apply text-light-accent-sport dark:text-dark-accent-sport
           bg-light-accent-sport/10 dark:bg-dark-accent-sport/10;
  }

  /* 🔍 Search Components */
  .search-input {
    @apply form-input pl-10 pr-4;
  }

  /* 📱 Mobile Menu */
  .mobile-menu {
    @apply fixed inset-0 z-50 bg-light-bg-primary dark:bg-dark-bg-primary
           transform transition-transform duration-300 ease-in-out;
  }

  .mobile-menu.closed {
    @apply translate-x-full;
  }

  /* 🛒 Product Card */
  .product-card {
    @apply card cursor-pointer overflow-hidden;
  }

  .product-card:hover {
    @apply shadow-xl transform -translate-y-1;
  }

  .product-image {
    @apply w-full h-48 object-cover transition-transform duration-300
           group-hover:scale-105;
  }

  /* 💰 Price Display */
  .price {
    @apply font-bold text-lg text-light-accent-sport dark:text-dark-accent-sport;
  }

  .price-old {
    @apply text-sm text-light-text-muted dark:text-dark-text-muted line-through;
  }

  /* 🏆 Sport Category Colors */
  .category-football {
    @apply bg-green-500/10 text-green-600 dark:text-green-400;
  }

  .category-basketball {
    @apply bg-orange-500/10 text-orange-600 dark:text-orange-400;
  }

  .category-tennis {
    @apply bg-yellow-500/10 text-yellow-600 dark:text-yellow-400;
  }

  .category-running {
    @apply bg-blue-500/10 text-blue-600 dark:text-blue-400;
  }

  .category-gym {
    @apply bg-purple-500/10 text-purple-600 dark:text-purple-400;
  }
}

@layer utilities {
  /* 🎨 Custom Utilities */
  .text-gradient {
    @apply bg-gradient-to-r from-light-accent-sport to-light-accent-info dark:from-dark-accent-sport dark:to-dark-accent-info
           bg-clip-text text-transparent;
  }

  .bg-gradient-sport {
    @apply bg-gradient-to-r from-light-accent-sport to-light-accent-info dark:from-dark-accent-sport dark:to-dark-accent-info;
  }

  .bg-gradient-danger {
    @apply bg-gradient-to-r from-light-accent-danger to-red-600 dark:from-dark-accent-danger dark:to-red-500;
  }

  /* 🌟 Glow Effects */
  .glow-sport {
    @apply shadow-[0_0_20px_rgba(0,200,151,0.3)] dark:shadow-[0_0_20px_rgba(0,229,161,0.3)];
  }

  .glow-danger {
    @apply shadow-[0_0_20px_rgba(239,35,60,0.3)] dark:shadow-[0_0_20px_rgba(255,71,87,0.3)];
  }

  /* 📐 Layout Utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-12 lg:py-20;
  }

  /* 🎭 Animation Utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-bounce-gentle {
    animation: bounceGentle 2s infinite;
  }

  /* 🔄 Loading States */
  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-light-accent-sport dark:border-dark-accent-sport;
  }

  .loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
  }

  /* 📱 Responsive Text */
  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl lg:text-3xl xl:text-4xl;
  }
}

/* 🎬 Custom Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceGentle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes loadingDots {
  0%, 20% {
    content: '.';
  }
  40% {
    content: '..';
  }
  60%, 100% {
    content: '...';
  }
}

/* 🎯 Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    @apply text-black bg-white;
  }

  .card {
    @apply border border-gray-300 shadow-none;
  }
}