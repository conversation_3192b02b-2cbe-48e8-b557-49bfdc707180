<template>
  <div class="space-y-8 p-6">
    <!-- 🎨 Gradient Buttons Section -->
    <section class="space-y-4">
      <h2 class="text-2xl font-bold text-gradient-sport">🔘 Gradient Buttons</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- Standard Gradient Button -->
        <div class="space-y-2">
          <h3 class="font-semibold text-sm text-light-text-secondary dark:text-dark-text-secondary">
            Standard Gradient
          </h3>
          <Button variant="primary" :gradient="true" size="md">
            Gradient Button
          </Button>
        </div>

        <!-- Animated Gradient Button -->
        <div class="space-y-2">
          <h3 class="font-semibold text-sm text-light-text-secondary dark:text-dark-text-secondary">
            Animated Gradient
          </h3>
          <Button variant="primary-animated" size="md">
            Animated Button
          </Button>
        </div>

        <!-- Danger Gradient Button -->
        <div class="space-y-2">
          <h3 class="font-semibold text-sm text-light-text-secondary dark:text-dark-text-secondary">
            Danger Gradient
          </h3>
          <Button variant="danger" size="md">
            Danger Button
          </Button>
        </div>

        <!-- Different Sizes -->
        <div class="space-y-2">
          <h3 class="font-semibold text-sm text-light-text-secondary dark:text-dark-text-secondary">
            Different Sizes
          </h3>
          <div class="flex items-center gap-2">
            <Button variant="primary" :gradient="true" size="sm">Small</Button>
            <Button variant="primary" :gradient="true" size="md">Medium</Button>
            <Button variant="primary" :gradient="true" size="lg">Large</Button>
          </div>
        </div>

        <!-- Rounded Gradient -->
        <div class="space-y-2">
          <h3 class="font-semibold text-sm text-light-text-secondary dark:text-dark-text-secondary">
            Rounded Gradient
          </h3>
          <Button variant="primary" :gradient="true" :rounded="true" size="md">
            Rounded Button
          </Button>
        </div>

        <!-- Full Width -->
        <div class="space-y-2">
          <h3 class="font-semibold text-sm text-light-text-secondary dark:text-dark-text-secondary">
            Full Width
          </h3>
          <Button variant="primary-animated" :full-width="true" size="md">
            Full Width Animated
          </Button>
        </div>
      </div>
    </section>

    <!-- 🌈 Gradient Text Section -->
    <section class="space-y-4">
      <h2 class="text-2xl font-bold text-gradient-sport">✨ Gradient Text</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Static Gradient Text -->
        <div class="space-y-3">
          <h3 class="font-semibold text-light-text-secondary dark:text-dark-text-secondary">
            Static Gradient Text
          </h3>
          <div class="space-y-2">
            <h1 class="text-4xl font-bold text-gradient-sport">
              Sport Shop
            </h1>
            <h2 class="text-2xl font-semibold text-gradient-sport">
              Premium Quality
            </h2>
            <p class="text-lg text-gradient-sport">
              Best sports equipment for athletes
            </p>
          </div>
        </div>

        <!-- Animated Gradient Text -->
        <div class="space-y-3">
          <h3 class="font-semibold text-light-text-secondary dark:text-dark-text-secondary">
            Animated Gradient Text
          </h3>
          <div class="space-y-2">
            <h1 class="text-4xl font-bold text-gradient-sport-animated">
              Dynamic Sport
            </h1>
            <h2 class="text-2xl font-semibold text-gradient-sport-animated">
              Live Action
            </h2>
            <p class="text-lg text-gradient-sport-animated">
              Experience the energy
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 🎯 Gradient Backgrounds Section -->
    <section class="space-y-4">
      <h2 class="text-2xl font-bold text-gradient-sport">🎯 Gradient Backgrounds</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- Soft Gradient Card -->
        <div class="bg-gradient-sport-soft p-6 rounded-lg">
          <h3 class="font-bold text-lg mb-2 text-light-text-primary dark:text-dark-text-primary">
            Soft Gradient
          </h3>
          <p class="text-light-text-secondary dark:text-dark-text-secondary">
            Subtle background gradient for cards and sections.
          </p>
        </div>

        <!-- Strong Gradient Card -->
        <div class="bg-gradient-sport dark:bg-gradient-sport-dark p-6 rounded-lg">
          <h3 class="font-bold text-lg mb-2 text-white">
            Strong Gradient
          </h3>
          <p class="text-white/90">
            Bold gradient for hero sections and call-to-action areas.
          </p>
        </div>

        <!-- Animated Background -->
        <div class="bg-gradient-sport-animated dark:bg-gradient-sport-animated-dark animate-gradient-x bg-[length:200%_200%] p-6 rounded-lg">
          <h3 class="font-bold text-lg mb-2 text-white">
            Animated Background
          </h3>
          <p class="text-white/90">
            Dynamic animated gradient for special sections.
          </p>
        </div>
      </div>
    </section>

    <!-- 🎪 Interactive Examples -->
    <section class="space-y-4">
      <h2 class="text-2xl font-bold text-gradient-sport">🎪 Interactive Examples</h2>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Product Card with Gradient -->
        <div class="bg-light-bg-secondary dark:bg-dark-bg-secondary rounded-lg overflow-hidden shadow-lg">
          <div class="bg-gradient-sport dark:bg-gradient-sport-dark h-32"></div>
          <div class="p-4">
            <h3 class="font-bold text-lg mb-2 text-gradient-sport">
              Premium Sports Shoes
            </h3>
            <p class="text-light-text-secondary dark:text-dark-text-secondary mb-4">
              High-performance athletic footwear for professional athletes.
            </p>
            <div class="flex gap-2">
              <Button variant="primary" :gradient="true" size="sm">
                Add to Cart
              </Button>
              <Button variant="primary-animated" size="sm">
                Buy Now
              </Button>
            </div>
          </div>
        </div>

        <!-- Hero Section Example -->
        <div class="bg-gradient-sport dark:bg-gradient-sport-dark rounded-lg p-8 text-center text-white">
          <h2 class="text-3xl font-bold mb-4">
            Welcome to Sport Shop
          </h2>
          <p class="text-white/90 mb-6">
            Discover the best sports equipment and gear for your athletic journey.
          </p>
          <div class="flex flex-col sm:flex-row gap-3 justify-center">
            <Button variant="secondary" size="lg">
              Browse Products
            </Button>
            <Button variant="primary-animated" size="lg">
              Shop Now
            </Button>
          </div>
        </div>
      </div>
    </section>

    <!-- 🎨 CSS Classes Reference -->
    <section class="space-y-4">
      <h2 class="text-2xl font-bold text-gradient-sport">📚 CSS Classes Reference</h2>
      
      <div class="bg-light-bg-secondary dark:bg-dark-bg-secondary rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="font-semibold mb-3 text-gradient-sport">Button Classes</h3>
            <ul class="space-y-1 text-sm font-mono text-light-text-secondary dark:text-dark-text-secondary">
              <li>• bg-gradient-sport</li>
              <li>• bg-gradient-sport-dark</li>
              <li>• bg-gradient-sport-animated</li>
              <li>• bg-gradient-danger</li>
              <li>• animate-gradient-x</li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-semibold mb-3 text-gradient-sport">Text Classes</h3>
            <ul class="space-y-1 text-sm font-mono text-light-text-secondary dark:text-dark-text-secondary">
              <li>• text-gradient-sport</li>
              <li>• text-gradient-sport-animated</li>
              <li>• bg-clip-text text-transparent</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import Button from './Button.vue'
</script>

<style scoped>
/* Additional styles for better gradient showcase */
.bg-gradient-sport-soft {
  @apply bg-gradient-sport-soft dark:bg-gradient-sport-dark-soft;
}
</style>
